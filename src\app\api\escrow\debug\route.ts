import { NextRequest, NextResponse } from 'next/server';
import { getEscrowTransactionByOrderId } from '@/services/transactionService';
import Strip<PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get('orderId');

    if (!orderId) {
      return NextResponse.json({
        success: false,
        error: 'Order ID is required'
      }, { status: 400 });
    }

    console.log('🔍 Debug: Checking escrow transaction for order:', orderId);

    // Get transaction details
    const transactionResult = await getEscrowTransactionByOrderId(orderId);
    
    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        success: false,
        error: 'Transaction not found',
        orderId
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // Check seller account status if available
    let sellerAccountStatus = null;
    if (transaction.sellerStripeAccountId) {
      try {
        const account = await stripe.accounts.retrieve(transaction.sellerStripeAccountId);
        sellerAccountStatus = {
          id: account.id,
          type: account.type,
          country: account.country,
          charges_enabled: account.charges_enabled,
          payouts_enabled: account.payouts_enabled,
          details_submitted: account.details_submitted,
          requirements: {
            currently_due: account.requirements?.currently_due || [],
            disabled_reason: account.requirements?.disabled_reason,
            pending_verification: account.requirements?.pending_verification || []
          }
        };
      } catch (error) {
        console.error('Error retrieving seller account:', error);
        sellerAccountStatus = { error: 'Failed to retrieve account details' };
      }
    }

    // Check payment intent status if available
    let paymentIntentStatus = null;
    if (transaction.stripePaymentIntentId) {
      try {
        const paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId);
        paymentIntentStatus = {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          amount_capturable: paymentIntent.amount_capturable,
          amount_received: paymentIntent.amount_received,
          capture_method: paymentIntent.capture_method,
          charges_count: paymentIntent.charges?.data?.length || 0,
          latest_charge: paymentIntent.charges?.data?.[0] ? {
            id: paymentIntent.charges.data[0].id,
            amount: paymentIntent.charges.data[0].amount,
            captured: paymentIntent.charges.data[0].captured,
            status: paymentIntent.charges.data[0].status
          } : null
        };
      } catch (error) {
        console.error('Error retrieving payment intent:', error);
        paymentIntentStatus = { error: 'Failed to retrieve payment intent details' };
      }
    }

    return NextResponse.json({
      success: true,
      debug: {
        transaction: {
          id: transaction.id,
          orderId: transaction.orderId,
          status: transaction.status,
          currentStage: transaction.currentStage,
          amount: transaction.amount,
          currency: transaction.currency,
          isEscrow: transaction.isEscrow,
          sellerId: transaction.sellerId,
          sellerEmail: transaction.sellerEmail,
          sellerStripeAccountId: transaction.sellerStripeAccountId,
          stripePaymentIntentId: transaction.stripePaymentIntentId,
          paymentAuthorized: transaction.paymentAuthorized,
          paymentCaptured: transaction.paymentCaptured,
          awaitingCapture: transaction.awaitingCapture,
          escrowStages: transaction.escrowStages?.map(stage => ({
            stage: stage.stage,
            percentage: stage.percentage,
            amount: stage.amount,
            amountInDollars: (stage.amount / 100).toFixed(2),
            status: stage.status,
            releasedAt: stage.releasedAt,
            stripeTransferId: stage.stripeTransferId
          }))
        },
        sellerAccount: sellerAccountStatus,
        paymentIntent: paymentIntentStatus,
        readyForCapture: paymentIntentStatus?.status === 'requires_capture',
        readyForTransfer: sellerAccountStatus?.charges_enabled && sellerAccountStatus?.payouts_enabled
      }
    });

  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json({
      success: false,
      error: 'Debug check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
