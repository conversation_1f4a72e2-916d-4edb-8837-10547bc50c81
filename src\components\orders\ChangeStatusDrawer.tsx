import React, { useState } from 'react';
import NestedDrawer from '../ui/nested-drawer';
import { CheckCircle, Circle } from 'react-feather';
import { CircleCheck } from 'lucide-react';

interface ChangeStatusDrawerProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  currentStatus: string;
  onStatusChange: (status: string) => void;
  orderType: 'placed' | 'received';
}

const placedStatuses = [
  { id: 'revision_request', label: 'Revision request' },
  { id: 'completed', label: 'Completed' },
];
const receivedStatuses = [
  { id: 'accept', label: 'Accept' },
  { id: 'incomplete', label: 'Incomplete' },
  { id: 'decline', label: 'Decline' },
];



const ChangeStatusDrawer: React.FC<ChangeStatusDrawerProps> = ({
  isOpen,
  onOpenChange,
  currentStatus,
  onStatusChange,
  orderType,
}) => {
  const [selectedStatus, setSelectedStatus] = useState(currentStatus);
  const [comment, setComment] = useState('');
  const [declineReasons, setDeclineReasons] = useState<string[]>([]);

  const declineOptions = [
    'I am not able to deliver the order',
    'I am currently overloaded with other orders',
    'Other',
  ];

  const handleConfirm = () => {
    onStatusChange(selectedStatus);
    onOpenChange(false);
  };

  return (
    <NestedDrawer
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title="Change Status"
    >
      <div className="flex flex-col h-full bg-white">
        {/* Content */}
        <div className="flex-1 p-4">
          {/* Status Selection */}
          <div className='flex flex-row gap-2 items-center'>
            <h3 className="text-sm font-semibold text-gray-600">Status</h3>
            <div className="flex flex-row gap-2 items-center">
              {(orderType === 'received' ? receivedStatuses : placedStatuses).map((status) => (
                <button
                  key={status.id}
                  onClick={() => setSelectedStatus(status.id)}
                  className={`px-4 py-2.5 text-sm transition-colors rounded-lg ${selectedStatus === status.id
                    ? 'bg-black text-white font-medium'
                    : 'bg-[#F5F5F5] text-gray-900 hover:bg-gray-200'
                    }`}
                >
                  {status.label}
                </button>
              ))}
            </div>
          </div>
          <div>

            {selectedStatus === 'accept' && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                You're confirming that you're accepting the order. The buyer will receive a confirmation message.
              </div>
            )}
            {selectedStatus === 'incomplete' && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                Incomplete order information. You can request additional information within 48 hours of receiving the order
              </div>
            )}
            {selectedStatus === 'decline' && (
              <>
                <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                  You're about to reject this order. Please let the buyer know why.
                </div>
                <div className="mt-2 mb-2">
                  <h4 className="text-sm font-medium mb-2">Reason</h4>
                  {declineOptions.map((reason) => (
                    <label
                      key={reason}
                      className={`flex items-center gap-2 mb-1 text-sm cursor-pointer ${declineReasons.includes(reason) ? 'text-primary' : 'text-[#828282]'}`}
                    >
                      <input
                        type="checkbox"
                        value={reason}
                        checked={declineReasons.includes(reason)}
                        onChange={e => {
                          if (e.target.checked) {
                            setDeclineReasons([...declineReasons, reason]);
                          } else {
                            setDeclineReasons(declineReasons.filter(r => r !== reason));
                          }
                        }}
                        className="hidden"
                      />
                      {declineReasons.includes(reason) ? <CheckCircle /> : <Circle />}
                      {reason}
                    </label>
                  ))}
                </div>
              </>
            )}

            {selectedStatus === 'revision_request' && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
                The work is not yet delivered, or accepted as delivered. The task needs to be completed or revised.
              </div>
            )}
            {selectedStatus === 'completed' && (
              <div className="mt-4 p-3 text-[#828282] rounded text-sm">
            You're confirming that the work has been completed and is accepted.
              </div>
            )}
          </div>

          {/* Comment Field */}
          <div className="mt-6">
            <h3 className="text-sm font-normal text-gray-600 mb-3">Comment</h3>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Your comment"
              className="w-full h-[120px] p-3 border border-gray-200 rounded-lg resize-none text-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black placeholder:text-gray-400"
            />
          </div>
        </div>

        {/* Footer with Confirm Button */}
        <div className="p-4 border-t border-gray-100">
          <button
            onClick={handleConfirm}
            className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2"
            disabled={!selectedStatus || !comment.trim()}

          >
            Confirm
          </button>
        </div>
      </div>
    </NestedDrawer>
  );
};

export default ChangeStatusDrawer; 