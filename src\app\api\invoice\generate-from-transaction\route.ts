import { NextRequest, NextResponse } from 'next/server';
import { getTransaction } from '@/services/transactionService';
import Strip<PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);

export async function POST(request: NextRequest) {
  try {
    const { transactionId, includeChargeDetails = true, isUS = false } = await request.json();

    if (!transactionId) {
      return NextResponse.json({
        success: false,
        error: 'Transaction ID is required'
      }, { status: 400 });
    }

    console.log('🧾 ===== GENERATING INVOICE FROM TRANSACTION =====');
    console.log(`📋 Transaction ID: ${transactionId}`);
    console.log(`🔍 Include Charge Details: ${includeChargeDetails}`);
    console.log(`🌍 Using US Stripe: ${isUS}`);

    // Step 1: Get transaction from Firebase
    console.log('🔄 Step 1: Fetching transaction from Firebase...');
    const transactionResult = await getTransaction(transactionId);

    if (!transactionResult.success || !transactionResult.transaction) {
      return NextResponse.json({
        success: false,
        error: 'Transaction not found'
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;
    console.log('✅ Transaction found:', {
      orderId: transaction.orderId,
      amount: transaction.amount,
      sessionId: transaction.sessionId
    });

    // Step 2: Get Stripe session to extract payment intent
    console.log('🔄 Step 2: Fetching Stripe session...');
    let paymentIntentId = null;
    let stripeSession = null;

    if (transaction.sessionId) {
      try {
        stripeSession = await stripe.checkout.sessions.retrieve(transaction.sessionId, {
          expand: ['payment_intent', 'payment_intent.charges']
        });
        paymentIntentId = stripeSession.payment_intent?.id || stripeSession.payment_intent;
        console.log('✅ Payment Intent ID extracted:', paymentIntentId);
      } catch (error) {
        console.warn('⚠️ Could not fetch Stripe session:', error);
      }
    }

    // Step 3: Get payment intent details
    console.log('🔄 Step 3: Fetching payment intent details...');
    let paymentIntent = null;
    let charges = [];

    if (paymentIntentId) {
      try {
        paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId, {
          expand: ['charges.data.payment_method_details', 'charges.data.billing_details']
        });
        charges = paymentIntent.charges?.data || [];
        console.log(`✅ Payment Intent retrieved with ${charges.length} charges`);
      } catch (error) {
        console.warn('⚠️ Could not fetch payment intent:', error);
      }
    }

    // Step 4: Get latest charge details
    console.log('🔄 Step 4: Processing charge details...');
    let latestCharge = null;
    if (charges.length > 0) {
      latestCharge = charges[0]; // Most recent charge
      console.log('✅ Latest charge found:', latestCharge.id);
    }

    // Step 5: Calculate money breakdown
    const moneyBreakdown = {
      originalAmount: transaction.amount,
      currency: transaction.currency || 'usd',
      transactionFee: Math.round(transaction.amount * 0.04), // 4% transaction fee
      platformCommission: Math.round(transaction.amount * 0.16), // 16% platform commission
      sellerAmount: Math.round(transaction.amount * 0.84), // 84% to seller
      escrowStages: {
        accept: Math.round(transaction.amount * 0.84 * 0.10), // 10% of seller amount
        delivered: Math.round(transaction.amount * 0.84 * 0.10), // 10% of seller amount
        completed: Math.round(transaction.amount * 0.84 * 0.80) // 80% of seller amount
      }
    };

    // Step 6: Create comprehensive invoice data
    const invoiceData = {
      // Invoice Metadata
      invoiceId: `INV-${transactionId}-${Date.now()}`,
      generatedAt: new Date().toISOString(),
      
      // Transaction Information
      transactionId: transactionId,
      orderId: transaction.orderId,
      
      // Payment Details
      paymentIntentId: paymentIntentId,
      chargeId: latestCharge?.id || null,
      sessionId: transaction.sessionId,
      
      // Customer Information
      customerId: stripeSession?.customer || transaction.userId,
      customerEmail: transaction.userEmail,
      
      // Money Breakdown
      amounts: {
        total: moneyBreakdown.originalAmount,
        totalFormatted: `$${(moneyBreakdown.originalAmount / 100).toFixed(2)}`,
        currency: moneyBreakdown.currency,
        breakdown: {
          subtotal: moneyBreakdown.originalAmount - moneyBreakdown.transactionFee,
          transactionFee: moneyBreakdown.transactionFee,
          platformCommission: moneyBreakdown.platformCommission,
          sellerAmount: moneyBreakdown.sellerAmount
        },
        escrowStages: moneyBreakdown.escrowStages
      },
      
      // Payment Status
      paymentStatus: {
        intent: paymentIntent?.status || 'unknown',
        charge: latestCharge?.status || 'unknown',
        captured: latestCharge?.captured || false,
        amountCaptured: latestCharge?.amount_captured || 0,
        amountRefunded: latestCharge?.amount_refunded || 0
      },
      
      // Stripe Objects (if requested)
      stripeDetails: includeChargeDetails ? {
        paymentIntent: paymentIntent,
        latestCharge: latestCharge,
        allCharges: charges,
        session: stripeSession
      } : null
    };

    console.log('✅ ===== INVOICE GENERATED SUCCESSFULLY =====');
    console.log(`🧾 Invoice ID: ${invoiceData.invoiceId}`);
    console.log(`💳 Payment Intent: ${invoiceData.paymentIntentId}`);
    console.log(`🔗 Charge ID: ${invoiceData.chargeId}`);
    console.log(`💰 Total Amount: ${invoiceData.amounts.totalFormatted}`);
    console.log(`📊 Escrow Stages:`, invoiceData.amounts.escrowStages);

    return NextResponse.json({
      success: true,
      invoice: invoiceData,
      summary: {
        transactionId,
        paymentIntentId: invoiceData.paymentIntentId,
        chargeId: invoiceData.chargeId,
        totalAmount: invoiceData.amounts.totalFormatted,
        status: invoiceData.paymentStatus
      }
    });

  } catch (error) {
    console.error('❌ Error generating invoice:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Failed to generate invoice',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// GET method for convenience
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const transactionId = searchParams.get('transaction_id');
    const includeChargeDetails = searchParams.get('include_charge_details') === 'true';
    const isUS = searchParams.get('isUS') === 'true';

    if (!transactionId) {
      return NextResponse.json({
        success: false,
        error: 'Transaction ID is required'
      }, { status: 400 });
    }

    // Convert to POST request format
    const postRequest = new NextRequest(request.url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ transactionId, includeChargeDetails, isUS })
    });

    return POST(postRequest);

  } catch (error) {
    console.error('❌ Error in GET invoice generation:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to generate invoice',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
