import React from 'react';
import { Clock, DollarSign } from 'react-feather';
import { format } from 'date-fns';
import RichTextFormatter from '../RichTextFormatter';

interface TimelineItem {
  type: 'order_status_update' | 'payment';
  date: string;
  status: string;
  description?: string;
  message?: string;
  amountPercent?: number;
  recipient?: string;
}

interface Order {
  id: string | number;
  status: string;
  orderNumber: string;
  image: string;
  userName: string;
  title: string;
  totalCost: number;
  activityLog:any[];
}

interface OrderDetailsTabProps {
  order: Order;
}


const orderTimeline: TimelineItem[] = [
  {
    type: "order_status_update",
    date: "2020-07-10T14:00:00",
    status: "NEW",
    description: "<PERSON> placed the order. <PERSON> have 48 hours to accept it."
  },
  {
    type: "payment",
    date: "2020-07-10T14:00:00",
    status: "Pre-authorized",
    description: "Full order amount pre-authorized by <PERSON><PERSON>"
  },
  {
    type: "order_status_update",
    date: "2020-07-10T15:00:00",
    status: "ACCEPTED",
    description: "<PERSON> accepted the order and will start working on it."
  },
  {
    type: "payment",
    date: "2020-07-10T15:00:00",
    status: "Received",
    amountPercent: 10,
    recipient: "<PERSON>"
  },
  {
    type: "order_status_update",
    date: "2020-07-11T16:00:00",
    status: "Delivered",
    description: "Ferdinand Karl delivered the order.",
    message: "Hope you love it."
  },
  {
    type: "payment",
    date: "2020-07-11T16:00:00",
    status: "Received",
    amountPercent: 10,
    recipient: "Ferdinand Karl"
  },
  {
    type: "order_status_update",
    date: "2020-07-11T19:00:00",
    status: "Completed",
    description: "John Doe accepted the order.",
    message: "Thank you! I love it."
  },
  {
    type: "payment",
    date: "2020-07-11T19:00:00",
    status: "Received",
    amountPercent: 80,
    recipient: "Ferdinand Karl"
  }
];

const OrderActivityTab: React.FC<OrderDetailsTabProps>= ({ order }) => {
  // console.log(order.activityLog);
  
  const formatDate = (dateInput: any) => {
    let date: Date;
    if (dateInput && typeof dateInput === 'object' && 'seconds' in dateInput && 'nanoseconds' in dateInput) {
      // Firestore Timestamp
      date = new Date(dateInput.seconds * 1000 + dateInput.nanoseconds / 1e6);
    } else {
      // ISO string or Date
      date = new Date(dateInput);
    }
    if (isNaN(date.getTime())) return 'Invalid date';
    return format(date, 'MMM d, yyyy h:mm a');
  };

  return (
    <div className="space-y-4">
      {Array.isArray(order.activityLog) && order.activityLog.length > 0 ? (
        order.activityLog.map((item, index) => (
          <div key={index} className="flex gap-3">
            <div className="flex-1">
              <div className="flex flex-row items-center justify-between gap-2 mb-1">
                <p className="text-sm text-primary max-md:text-xs">{item.type}</p>
                <span className="text-sm text-gray-500 text-nowrap max-md:text-xs">
                  {formatDate(item.date)}
                </span>
              </div>
              <p className="font-bold text-gray-900 text-sm ">{item.title}</p>

              <p className="text-sm text-gray-600 mb-1">
                
                 <RichTextFormatter
                text={item.description}
                className="mt-2 text-subtitle"
                preserveWhitespace={true}
                enableMarkdown={true}
              />
              </p>
           
            </div>
          </div>
        ))
      ) : (
        <div className="py-8 text-center text-gray-500">No activity yet for this order.</div>
      )}
    </div>
  );
};

export default OrderActivityTab; 