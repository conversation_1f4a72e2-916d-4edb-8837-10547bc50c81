import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { updateTransaction, getTransactionByStripeSessionId, getEscrowTransactionByOrderId } from '@/services/transactionService';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);
const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET as string;

export async function POST(request: NextRequest) {
  try {
    const body = await request.text();
    const signature = request.headers.get('stripe-signature') as string;

    let event: Stripe.Event;

    try {
      event = stripe.webhooks.constructEvent(body, signature, webhookSecret);
    } catch (err) {
      console.error('Webhook signature verification failed:', err);
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
    }

    console.log('Received Stripe webhook event:', event.type);

    // Handle the event
    switch (event.type) {
      case 'checkout.session.completed':
        await handleCheckoutSessionCompleted(event.data.object as Stripe.Checkout.Session);
        break;

      case 'payment_intent.succeeded':
        await handlePaymentIntentSucceeded(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.requires_capture':
        await handlePaymentIntentRequiresCapture(event.data.object as Stripe.PaymentIntent);
        break;

      case 'payment_intent.payment_failed':
        await handlePaymentIntentFailed(event.data.object as Stripe.PaymentIntent);
        break;

      case 'invoice.payment_succeeded':
        await handleInvoicePaymentSucceeded(event.data.object as Stripe.Invoice);
        break;

      case 'customer.created':
        await handleCustomerCreated(event.data.object as Stripe.Customer);
        break;

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error('Webhook error:', error);
    return NextResponse.json({ error: 'Webhook handler failed' }, { status: 500 });
  }
}

async function handleCheckoutSessionCompleted(session: Stripe.Checkout.Session) {
  try {
    console.log('Processing checkout session completed:', session.id);
    
    // Get transaction by session ID
    const transactionResult = await getTransactionByStripeSessionId(session.id);
    
    if (transactionResult.success && transactionResult.transaction) {
      const transaction = transactionResult.transaction;

      // Check if this is an escrow transaction with manual capture
      if (transaction.isEscrow && session.payment_intent) {
        try {
          const paymentIntent = await stripe.paymentIntents.retrieve(session.payment_intent as string);

          console.log('Escrow Payment Intent Status:', {
            id: paymentIntent.id,
            status: paymentIntent.status,
            capture_method: paymentIntent.capture_method
          });

          if (paymentIntent.capture_method === 'manual' && paymentIntent.status === 'requires_capture') {
            // For manual capture escrow, payment is authorized but not captured
            await updateTransaction(transaction.id, {
              status: 'pending', // Keep as pending until manually captured
              stripePaymentIntentId: session.payment_intent as string,
              paymentAuthorized: true,
              paymentAuthorizedAt: new Date(),
              awaitingCapture: true,
              metadata: {
                ...transaction.metadata,
                stripeSessionData: {
                  id: session.id,
                  amount_total: session.amount_total,
                  currency: session.currency,
                  customer: session.customer,
                  payment_status: session.payment_status,
                },
                paymentIntentStatus: paymentIntent.status,
                escrowStage: 'authorized_awaiting_capture'
              },
            });

            console.log(`Escrow transaction ${transaction.id} - payment authorized, awaiting manual capture`);

          } else if (paymentIntent.status === 'succeeded') {
            // Payment was captured (shouldn't happen with manual capture, but handle it)
            const chargeId = paymentIntent.charges?.data?.[0]?.id;

            await updateTransaction(transaction.id, {
              status: 'completed',
              stripePaymentIntentId: session.payment_intent as string,
              paymentCaptured: true,
              paymentCapturedAt: new Date(),
              completedAt: new Date(),
              metadata: {
                ...transaction.metadata,
                stripeSessionData: {
                  id: session.id,
                  amount_total: session.amount_total,
                  currency: session.currency,
                  customer: session.customer,
                  payment_status: session.payment_status,
                },
                chargeId,
                escrowReady: true,
                paymentIntentStatus: paymentIntent.status
              },
            });

            console.log(`Escrow transaction ${transaction.id} - payment captured automatically. Charge ID: ${chargeId}`);
          }

        } catch (error) {
          console.error('Error retrieving payment intent for escrow:', error);
        }
      } else {
        // Non-escrow transaction - mark as completed
        await updateTransaction(transaction.id, {
          status: 'completed',
          stripePaymentIntentId: session.payment_intent as string,
          completedAt: new Date(),
          metadata: {
            ...transaction.metadata,
            stripeSessionData: {
              id: session.id,
              amount_total: session.amount_total,
              currency: session.currency,
              customer: session.customer,
              payment_status: session.payment_status,
            },
          },
        });

        console.log(`Non-escrow transaction ${transaction.id} marked as completed`);
      }
    } else {
      console.error('Could not find transaction for session:', session.id);
    }
  } catch (error) {
    console.error('Error handling checkout session completed:', error);
  }
}

async function handlePaymentIntentSucceeded(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing payment intent succeeded:', paymentIntent.id);

    // Check if this is an escrow payment that was captured
    if (paymentIntent.metadata?.isEscrow === 'true') {
      console.log('Escrow payment succeeded - payment captured and ready for transfers');

      // Update transaction status if needed
      const orderId = paymentIntent.metadata.orderId;
      if (orderId) {
        const transactionResult = await getEscrowTransactionByOrderId(orderId);
        if (transactionResult.success && transactionResult.transaction) {
          await updateTransaction(transactionResult.transaction.id, {
            status: 'completed',
            metadata: {
              ...transactionResult.transaction.metadata,
              paymentCapturedAt: new Date().toISOString(),
              paymentIntentStatus: paymentIntent.status
            }
          });
        }
      }
    }

  } catch (error) {
    console.error('Error handling payment intent succeeded:', error);
  }
}

async function handlePaymentIntentRequiresCapture(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing payment intent requires capture:', paymentIntent.id);

    // Check if this is an escrow payment
    if (paymentIntent.metadata?.isEscrow === 'true') {
      console.log('Escrow payment authorized - waiting for capture trigger');

      const orderId = paymentIntent.metadata.orderId;
      if (orderId) {
        // Update transaction to reflect authorization status
        const transactionResult = await getEscrowTransactionByOrderId(orderId);
        if (transactionResult.success && transactionResult.transaction) {
          await updateTransaction(transactionResult.transaction.id, {
            currentStage: 'pending', // Payment authorized but not captured
            metadata: {
              ...transactionResult.transaction.metadata,
              paymentAuthorizedAt: new Date().toISOString(),
              paymentIntentStatus: paymentIntent.status,
              awaitingCapture: true
            }
          });

          console.log(`Escrow transaction ${transactionResult.transaction.id} updated - payment authorized, awaiting capture`);
        }
      }
    }

  } catch (error) {
    console.error('Error handling payment intent requires capture:', error);
  }
}

async function handlePaymentIntentFailed(paymentIntent: Stripe.PaymentIntent) {
  try {
    console.log('Processing payment intent failed:', paymentIntent.id);
    
    // You can add logic here to handle failed payments
    // For example, notifying the user, updating transaction status, etc.
    
  } catch (error) {
    console.error('Error handling payment intent failed:', error);
  }
}

async function handleInvoicePaymentSucceeded(invoice: Stripe.Invoice) {
  try {
    console.log('Processing invoice payment succeeded:', invoice.id);
    
    // Handle invoice payment success
    // This is useful for subscription payments
    
  } catch (error) {
    console.error('Error handling invoice payment succeeded:', error);
  }
}

async function handleCustomerCreated(customer: Stripe.Customer) {
  try {
    console.log('Processing customer created:', customer.id);
    
    // You can add logic here to sync customer data
    // For example, updating user records in Firebase
    
  } catch (error) {
    console.error('Error handling customer created:', error);
  }
}

// Handle other HTTP methods
export async function GET() {
  return NextResponse.json({ message: 'Stripe webhook endpoint' });
}

export async function PUT() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}

export async function DELETE() {
  return NextResponse.json({ error: 'Method not allowed' }, { status: 405 });
}
