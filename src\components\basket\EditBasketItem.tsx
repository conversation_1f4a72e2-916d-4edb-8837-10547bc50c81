import React, { useState, useEffect } from "react";
import { CheckSquare, Info, Square } from "react-feather";
import CustomizationDetails from "./CustomizationDetails";
import NestedDrawer from "../ui/nested-drawer";
import { BasketItem, CustomizationOption } from "../../types/basket"; // Import from types
import { updateOrder } from "../../services/ordersServices"; // Import updateOrder service
import RichTextFormatter from "../RichTextFormatter";

interface EditBasketItemProps {
  selectedItem: BasketItem | null;
  onSave: (item: BasketItem) => void;
  onClose: () => void;
  currencySymbol: string;
}

const EditBasketItem: React.FC<EditBasketItemProps> = ({
  selectedItem,
  onSave,
  onClose,
  currencySymbol,
}) => {
  if (!selectedItem) return null;

  console.log("EditBasketItem received data:", {
    selectedItem,
  });

  // Get customizations from selectedItem
  const customizationOptions = selectedItem.serviceDetails?.customizations || [];

  const [editedItem, setEditedItem] = useState<BasketItem>({ ...selectedItem });
  const [isCustomizationDetailsOpen, setIsCustomizationDetailsOpen] = useState(false);
  const [selectedCustomization, setSelectedCustomization] = useState<CustomizationOption | null>(
    null
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // Initialize isCheck based on selectedCustomizations
  const [isCheck, setIsCheck] = useState<boolean[]>(() => {
    return customizationOptions.map(
      (option) => selectedItem.selectedCustomizations?.includes(option.id) || false
    );
  });

  // Update isCheck when selectedItem changes
  useEffect(() => {
    setIsCheck(
      customizationOptions.map(
        (option) => selectedItem.selectedCustomizations?.includes(option.id) || false
      )
    );
  }, [selectedItem.selectedCustomizations, customizationOptions.length]);

  // Calculate total cost including selected customizations
  const calculateTotalCost = () => {
    let total = selectedItem.subtotal;

    customizationOptions.forEach((option, index) => {
      if (isCheck[index] && option.price) {
        total += parseFloat(option.price);
      }
    });

    return total;
  };

  // Calculate total time including selected customizations
  const calculateTotalTime = () => {
    let baseDuration = parseFloat(selectedItem.serviceDetails?.duration || "0");

    customizationOptions.forEach((option, index) => {
      if (isCheck[index] && option.duration) {
        baseDuration += parseFloat(option.duration);
      }
    });

    return `${baseDuration}d`;
  };

  const handleSave = async () => {
    try {
      setIsUpdating(true);
      setError(null);
      setSuccess(false);

      const updatedItem = {
        ...editedItem,
        subtotal: calculateTotalCost(),
        time: calculateTotalTime(),
      };

      // Prepare the data for Firebase update
      const updateData = {
        comment: updatedItem.orderDetails.comment || "",
        selectedCustomizations: updatedItem.selectedCustomizations || [],
      };

      // Call the updateOrder service to persist changes to Firebase
      const result = await updateOrder(selectedItem.orderId || selectedItem.id.toString(), updateData);

      if (result.success) {
        setSuccess(true);
        // Call the onSave callback with the updated item to refresh parent component
        onSave(updatedItem);
        // Close the drawer after a short delay to show success message
        setTimeout(() => {
          onClose();
        }, 1000);
      } else {
        setError(result.error || "Failed to update basket item");
        console.error("Failed to update basket item:", result.error);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
      setError(errorMessage);
      console.error("Error updating basket item:", error);
    } finally {
      setIsUpdating(false);
    }
  };

  const handleIsCheck = (index: number) => {
    const newIsCheck = [...isCheck];
    newIsCheck[index] = !newIsCheck[index];
    setIsCheck(newIsCheck);

    const option = customizationOptions[index];
    const currentSelectedCustomizations = editedItem.selectedCustomizations || [];
    const isSelected = currentSelectedCustomizations.includes(option.id);

    // Update selectedCustomizations in editedItem
    setEditedItem({
      ...editedItem,
      selectedCustomizations: isSelected
        ? currentSelectedCustomizations.filter((id) => id !== option.id)
        : [...currentSelectedCustomizations, option.id],
      orderDetails: {
        ...editedItem.orderDetails,
        selectedOptions: isSelected
          ? (editedItem.orderDetails.selectedOptions || []).filter((id) => id !== option.id)
          : [...(editedItem.orderDetails.selectedOptions || []), option.id],
      },
    });
  };

  const handleInfoClick = (optionId: string) => {
    const option = customizationOptions.find((opt) => opt.id === optionId);
    if (option) {
      setSelectedCustomization(option);
      setIsCustomizationDetailsOpen(true);
    }
  };

  return (
    <>
      <div className="space-y-6 px-4 max-md:px-2">
        {/* Service Details */}
        <section>
          <h3 className="font-semibold mb-2">{selectedItem.title}</h3>
          <p className="text-gray-600 text-sm mb-4">{selectedItem.time}</p>
          <p className="text-gray-600 text-sm">
            <RichTextFormatter
              text={selectedItem.description || ""}
              className="text-gray-600 text-sm"
              preserveWhitespace={true}
              enableMarkdown={true}
            />

          </p>
        </section>

        {/* Customization Options */}
        <section>
          <h3 className="font-semibold mb-4">Customization</h3>

          {/* Check if customizations exist */}
          {customizationOptions.length > 0 ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 text-sm font-medium text-gray-600 -mb-2">
                <div className="row gap-4">
                  <p className=" opacity-0  w-8 text-center">hi</p>
                  <div className="text-primary font-semibold w-14 text-center">Option</div>
                </div>
                <div className="justify-end row">
                  <div className="row gap-4">
                    <div className="text-primary font-semibold w-14 text-end">Time</div>
                    <div className="text-primary font-semibold w-14 text-center">Cost</div>
                    <p className=" opacity-0  w-8 text-center">hi</p>
                  </div>
                </div>
              </div>

              {customizationOptions.map((option, index) => (
                <div key={option.id} className="grid grid-cols-2 ">
                  <div className="row gap-3">
                    <div className="flex items-center">
                      {isCheck[index] ? (
                        <CheckSquare
                          className="text-primary w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                          size={18}
                          onClick={() => handleIsCheck(index)}
                        />
                      ) : (
                        <Square
                          className="text-subtitle w-[18px] min-w-[18px] min-h-[18px] max-w-[18px] max-h-[18px] cursor-pointer"
                          size={18}
                          onClick={() => handleIsCheck(index)}
                        />
                      )}
                    </div>
                    <div
                      className={`cursor-pointer ${isCheck[index] ? "font-bold text-primary" : "font-normal text-subtitle"
                        }`}
                      onClick={() => handleIsCheck(index)}
                    >
                      {/* Display actual customization title with fallback */}
                      {option.title || `Option ${index + 1}`}
                    </div>
                  </div>

                  <div className="justify-end row">
                    <div className="row gap-4">
                      <div
                        className={`${isCheck[index] ? "font-bold text-primary" : "font-normal text-subtitle"
                          }`}
                      >
                        +{parseFloat(option.duration || "0").toFixed(0)}d
                      </div>
                      <div
                        className={`${isCheck[index] ? "font-bold text-primary" : "font-normal text-subtitle"
                          }`}
                      >
                        +{currencySymbol}
                        {parseFloat(option.price || "0").toFixed(2)}
                      </div>
                      <div className="flex justify-end">
                        <button
                          className="p-1 hover:bg-gray-100 rounded-full"
                          onClick={() => handleInfoClick(option.id)}
                        >
                          <Info size={18} className="text-subtitle" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">
              No customizations available for this service.
            </div>
          )}
        </section>

        {/* Comment Section */}
        <section>
          <h3 className="font-semibold mb-2">Comment</h3>
          <textarea
            value={editedItem.orderDetails.comment || ""}
            onChange={(e) =>
              setEditedItem({
                ...editedItem,
                orderDetails: {
                  ...editedItem.orderDetails,
                  comment: e.target.value,
                },
              })
            }
            placeholder="Hello! I want to order your service. I need this painting by June 23rd."
            className="w-full h-32 p-3 border rounded-lg resize-none text-sm"
          />
        </section>

        {/* Approximate Time and Total Cost */}
        <section className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Approximate time</span>
            <span className="font-medium">{calculateTotalTime()}</span>
          </div>
          <div className="flex justify-between text-sm">
            <span>Total cost</span>
            <span className="font-medium">
              {currencySymbol}
              {calculateTotalCost().toFixed(2)}
            </span>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            If for some reason you are not ready to make an order, just add it to the basket and you
            will be able to get back to it later.
          </p>
        </section>

        {/* Action Button */}
        {error && (
          <div className="text-red-500 text-sm mb-2 p-2 bg-red-50 rounded-md">
            {error}
          </div>
        )}

        {success && (
          <div className="text-green-600 text-sm mb-2 p-2 bg-green-50 rounded-md">
            Basket updated successfully!
          </div>
        )}

        <button
          className="btn-xs text-white btn py-4 w-full bg-primary rounded-full mt-2 disabled:opacity-50 disabled:cursor-not-allowed"
          onClick={handleSave}
          disabled={isUpdating || success}
        >
          {isUpdating ? "Updating..." : success ? "Updated!" : "Update Basket"}
        </button>
      </div>

      {/* Customization Details Drawer */}
      <NestedDrawer
        isOpen={isCustomizationDetailsOpen}
        onOpenChange={setIsCustomizationDetailsOpen}
        title="Customization Details"
      >
        {selectedCustomization && (
          <CustomizationDetails
            id={selectedItem.id.toString()}
            custamizationId={selectedCustomization.id}
            onSelectService={() => setIsCustomizationDetailsOpen(false)}
            currencySymbol={currencySymbol}
            selectedOption={selectedCustomization}
          />
        )}
      </NestedDrawer>
    </>
  );
};

export default EditBasketItem;
