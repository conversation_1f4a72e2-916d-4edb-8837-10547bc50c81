import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error("STRIPE_SECRET_KEY is not defined");
}

// Following the same pattern as Firebase Functions
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const stripeUS = new Stripe(process.env.STRIPE_SECRET_KEY); // Add US stripe if needed

// Get latest charge data for a payment intent - Following Firebase Functions pattern
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const paymentIntentId = searchParams.get('payment_intent_id');
    const isUS = searchParams.get('isUS') === 'true';

    if (!paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID is required'
      }, { status: 400 });
    }

    // Following Firebase Functions pattern - select stripe service
    let stripeService = isUS ? stripeUS : stripe;

    console.log('🔍 ===== STRIPE GET CHARGE FROM PAYMENT INTENT =====');
    console.log(`📋 Payment Intent ID: ${paymentIntentId}`);
    console.log(`🌍 Using US Stripe: ${isUS}`);
    console.log('🕐 Timestamp:', new Date().toISOString());

    // Following Firebase Functions pattern - retrieve payment intent with expanded charges
    console.log('🔄 Retrieving payment intent with expanded charges...');
    const paymentIntent = await stripeService.paymentIntents.retrieve(paymentIntentId, {
      expand: ['charges.data', 'charges.data.payment_method_details', 'charges.data.billing_details', 'charges.data.outcome']
    });

    console.log('✅ PAYMENT INTENT RETRIEVED:');
    console.log(`💳 Status: ${paymentIntent.status}`);
    console.log(`💰 Amount: ${paymentIntent.amount}`);
    console.log(`💰 Amount Capturable: ${paymentIntent.amount_capturable}`);
    console.log(`💰 Amount Received: ${paymentIntent.amount_received}`);
    console.log(`💱 Currency: ${paymentIntent.currency}`);
    console.log(`⚡ Latest Charge: ${paymentIntent.latest_charge}`);
    console.log(`📋 Charges Count: ${paymentIntent.charges.data.length}`);
    console.log(`🔧 Capture Method: ${paymentIntent.capture_method}`);
    console.log(`👤 Customer: ${paymentIntent.customer}`);
    console.log(`💳 Payment Method: ${paymentIntent.payment_method}`);
    console.log(`📧 Receipt Email: ${paymentIntent.receipt_email}`);

    if (!paymentIntent.charges.data || paymentIntent.charges.data.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No charges found for this payment intent',
        paymentIntent: {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          currency: paymentIntent.currency
        }
      }, { status: 404 });
    }

    // Get the latest charge (most recent)
    const latestCharge = paymentIntent.charges.data[0];

    console.log('🎯 ===== LATEST CHARGE DETAILS =====');
    console.log(`🆔 Charge ID: ${latestCharge.id}`);
    console.log(`📊 Status: ${latestCharge.status}`);
    console.log(`💰 Amount: ${latestCharge.amount}`);
    console.log(`💰 Amount Captured: ${latestCharge.amount_captured}`);
    console.log(`💰 Amount Refunded: ${latestCharge.amount_refunded}`);
    console.log(`💱 Currency: ${latestCharge.currency}`);
    console.log(`✅ Paid: ${latestCharge.paid}`);
    console.log(`📦 Captured: ${latestCharge.captured}`);
    console.log(`↩️ Refunded: ${latestCharge.refunded}`);
    console.log(`🕐 Created: ${new Date(latestCharge.created * 1000).toISOString()}`);
    console.log(`📧 Receipt Email: ${latestCharge.receipt_email}`);
    console.log(`🧾 Receipt URL: ${latestCharge.receipt_url}`);
    console.log(`💳 Payment Method: ${latestCharge.payment_method}`);
    console.log(`📝 Description: ${latestCharge.description}`);

    if (latestCharge.payment_method_details?.card) {
      console.log('💳 ===== CARD DETAILS =====');
      console.log(`🏷️ Brand: ${latestCharge.payment_method_details.card.brand}`);
      console.log(`🔢 Last 4: ${latestCharge.payment_method_details.card.last4}`);
      console.log(`📅 Exp: ${latestCharge.payment_method_details.card.exp_month}/${latestCharge.payment_method_details.card.exp_year}`);
      console.log(`🌍 Country: ${latestCharge.payment_method_details.card.country}`);
      console.log(`💰 Funding: ${latestCharge.payment_method_details.card.funding}`);
      console.log(`🔐 Network: ${latestCharge.payment_method_details.card.network}`);
    }

    if (latestCharge.billing_details) {
      console.log('📍 ===== BILLING DETAILS =====');
      console.log(`👤 Name: ${latestCharge.billing_details.name}`);
      console.log(`📧 Email: ${latestCharge.billing_details.email}`);
      console.log(`📞 Phone: ${latestCharge.billing_details.phone}`);
      if (latestCharge.billing_details.address) {
        console.log(`🏠 Address:`, latestCharge.billing_details.address);
      }
    }

    if (latestCharge.outcome) {
      console.log('🔒 ===== TRANSACTION OUTCOME =====');
      console.log(`📊 Type: ${latestCharge.outcome.type}`);
      console.log(`🌐 Network Status: ${latestCharge.outcome.network_status}`);
      console.log(`⚠️ Risk Level: ${latestCharge.outcome.risk_level}`);
      console.log(`🎯 Risk Score: ${latestCharge.outcome.risk_score}`);
      console.log(`💬 Seller Message: ${latestCharge.outcome.seller_message}`);
    }

    // Following Firebase Functions pattern - also get all charges for this payment intent
    console.log('🔄 Fetching all charges via charges.list...');
    const allCharges = await stripeService.charges.list({
      payment_intent: paymentIntentId,
      limit: 10,
      expand: ['data.payment_method_details', 'data.billing_details', 'data.outcome']
    });

    console.log(`📋 ===== ALL CHARGES SUMMARY =====`);
    console.log(`📊 Total charges found: ${allCharges.data.length}`);
    allCharges.data.forEach((charge, index) => {
      console.log(`🎯 Charge ${index + 1}: ${charge.id} - Status: ${charge.status} - Amount: ${charge.amount}`);
    });

    // Following Firebase Functions response pattern
    const responseData = {
      success: true,
      isUSStripeUsed: Boolean(isUS),
      paymentIntent: paymentIntent,
      latestCharge: latestCharge,
      chargeId: latestCharge ? latestCharge.id : null,
      charges: allCharges.data,
      chargeCount: allCharges.data.length,
      timestamp: new Date().toISOString()
    };

    console.log('✅ ===== STRIPE GET CHARGE RESPONSE SUMMARY =====');
    console.log(`🎯 Returning charge ID: ${responseData.chargeId}`);
    console.log(`📊 Payment status: ${responseData.latestCharge?.status}`);
    console.log(`💰 Amount: ${responseData.latestCharge?.amount} ${responseData.latestCharge?.currency}`);
    console.log(`📦 Captured: ${responseData.latestCharge?.captured}`);
    console.log(`📋 Total charges: ${responseData.chargeCount}`);
    console.log(`🌍 US Stripe Used: ${responseData.isUSStripeUsed}`);
    console.log('🔚 ===== END STRIPE GET CHARGE =====');

    return NextResponse.json(responseData);

  } catch (error) {
    console.error('Error fetching charge data:', error);
    
    if (error instanceof Stripe.errors.StripeError) {
      return NextResponse.json({
        error: 'Stripe API error',
        details: error.message,
        type: error.type
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Failed to fetch charge data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// POST method to refresh charge data - Following Firebase Functions pattern
export async function POST(request: NextRequest) {
  try {
    const { paymentIntentId, isUS } = await request.json();

    if (!paymentIntentId) {
      return NextResponse.json({
        success: false,
        error: 'Payment Intent ID is required'
      }, { status: 400 });
    }

    console.log('🔍 ===== STRIPE GET CHARGE FROM PAYMENT INTENT (POST) =====');
    console.log(`📋 Payment Intent ID: ${paymentIntentId}`);
    console.log(`🌍 Using US Stripe: ${isUS === 'true'}`);

    // Following Firebase Functions pattern - select stripe service
    let stripeService = isUS === 'true' ? stripeUS : stripe;

    // Retrieve payment intent with expanded charges
    const paymentIntent = await stripeService.paymentIntents.retrieve(paymentIntentId, {
      expand: ['charges.data', 'charges.data.payment_method_details', 'charges.data.billing_details', 'charges.data.outcome']
    });

    // Get the latest charge
    const latestCharge = paymentIntent.charges.data[0];

    // Also get all charges for this payment intent
    const allCharges = await stripeService.charges.list({
      payment_intent: paymentIntentId,
      limit: 10,
      expand: ['data.payment_method_details', 'data.billing_details', 'data.outcome']
    });

    console.log(`🎯 Latest Charge ID: ${latestCharge?.id}`);
    console.log(`📊 Charge Status: ${latestCharge?.status}`);
    console.log(`📋 Total charges found: ${allCharges.data.length}`);

    return NextResponse.json({
      success: true,
      isUSStripeUsed: Boolean(isUS === 'true'),
      paymentIntent: paymentIntent,
      latestCharge: latestCharge,
      chargeId: latestCharge ? latestCharge.id : null,
      charges: allCharges.data,
      chargeCount: allCharges.data.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Error in POST charge data:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to refresh charge data',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
