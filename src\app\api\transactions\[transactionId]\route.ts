import { NextRequest, NextResponse } from 'next/server';
import { getTransaction } from '@/services/transactionService';
import Strip<PERSON> from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY as string);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ transactionId: string }> }
) {
  try {
    const { transactionId } = await params;

    console.log('🔍 ===== TRANSACTION API CALLED =====');
    console.log('📋 Transaction ID:', transactionId);
    console.log('🕐 Timestamp:', new Date().toISOString());

    if (!transactionId) {
      console.log('❌ No transaction ID provided');
      return NextResponse.json({ error: 'Transaction ID is required' }, { status: 400 });
    }

    console.log('🔄 Fetching transaction from Firebase...');
    // Get transaction from Firebase
    const transactionResult = await getTransaction(transactionId);

    console.log('📊 Firebase Transaction Result:', {
      success: transactionResult.success,
      hasTransaction: !!transactionResult.transaction,
      transactionData: transactionResult.transaction ? {
        id: transactionResult.transaction.id,
        orderId: transactionResult.transaction.orderId,
        stripeSessionId: transactionResult.transaction.stripeSessionId,
        stripePaymentIntentId: transactionResult.transaction.stripePaymentIntentId,
        amount: transactionResult.transaction.amount,
        status: transactionResult.transaction.status
      } : null
    });

    if (!transactionResult.success) {
      return NextResponse.json({
        success: false,
        error: transactionResult.error || 'Transaction not found',
      }, { status: 404 });
    }

    const transaction = transactionResult.transaction;

    // Get additional Stripe data if available
    let stripeSession = null;
    let paymentIntent = null;

    try {
      console.log('🔄 Fetching Stripe data...');
      console.log('🎫 Stripe Session ID:', transaction.stripeSessionId);
      console.log('💳 Stripe Payment Intent ID:', transaction.stripePaymentIntentId);

      // Get session details if session ID exists
      if (transaction.stripeSessionId) {
        console.log('🔄 Retrieving Stripe session...');
        stripeSession = await stripe.checkout.sessions.retrieve(transaction.stripeSessionId, {
          expand: ['payment_intent', 'customer'],
        });
        console.log('✅ Stripe Session Retrieved:', {
          id: stripeSession.id,
          payment_status: stripeSession.payment_status,
          amount_total: stripeSession.amount_total,
          currency: stripeSession.currency,
          customer: stripeSession.customer,
          payment_intent: stripeSession.payment_intent
        });
      }

      // Get payment intent if available
      if (transaction.stripePaymentIntentId) {
        console.log('🔄 Retrieving Payment Intent by ID...');
        paymentIntent = await stripe.paymentIntents.retrieve(transaction.stripePaymentIntentId, {
          expand: ['charges.data']
        });
      } else if (stripeSession?.payment_intent) {
        // Get payment intent from session
        const piId = typeof stripeSession.payment_intent === 'string'
          ? stripeSession.payment_intent
          : stripeSession.payment_intent.id;

        console.log('🔄 Retrieving Payment Intent from session:', piId);
        paymentIntent = await stripe.paymentIntents.retrieve(piId, {
          expand: ['charges.data']
        });
      }

      if (paymentIntent) {
        console.log('✅ Payment Intent Retrieved:', {
          id: paymentIntent.id,
          status: paymentIntent.status,
          amount: paymentIntent.amount,
          amount_capturable: paymentIntent.amount_capturable,
          amount_received: paymentIntent.amount_received,
          currency: paymentIntent.currency,
          latest_charge: paymentIntent.latest_charge,
          charges_count: paymentIntent.charges?.data?.length || 0,
          capture_method: paymentIntent.capture_method,
          customer: paymentIntent.customer,
          payment_method: paymentIntent.payment_method,
          receipt_email: paymentIntent.receipt_email
        });

        if (paymentIntent.charges?.data?.length > 0) {
          console.log('💳 CHARGES FOUND:', paymentIntent.charges.data.length);
          paymentIntent.charges.data.forEach((charge, index) => {
            console.log(`🎯 CHARGE ${index + 1} DETAILS:`, {
              id: charge.id,
              status: charge.status,
              amount: charge.amount,
              amount_captured: charge.amount_captured,
              amount_refunded: charge.amount_refunded,
              currency: charge.currency,
              paid: charge.paid,
              captured: charge.captured,
              refunded: charge.refunded,
              created: new Date(charge.created * 1000).toISOString(),
              payment_method: charge.payment_method,
              receipt_email: charge.receipt_email,
              receipt_url: charge.receipt_url
            });

            if (charge.payment_method_details?.card) {
              console.log(`💳 CARD DETAILS for ${charge.id}:`, {
                brand: charge.payment_method_details.card.brand,
                last4: charge.payment_method_details.card.last4,
                exp_month: charge.payment_method_details.card.exp_month,
                exp_year: charge.payment_method_details.card.exp_year,
                country: charge.payment_method_details.card.country
              });
            }

            if (charge.billing_details) {
              console.log(`📍 BILLING DETAILS for ${charge.id}:`, {
                name: charge.billing_details.name,
                email: charge.billing_details.email,
                phone: charge.billing_details.phone,
                address: charge.billing_details.address
              });
            }

            if (charge.outcome) {
              console.log(`🔒 OUTCOME for ${charge.id}:`, charge.outcome);
            }
          });
        } else {
          console.log('⚠️ NO CHARGES FOUND in payment intent');
        }
      } else {
        console.log('⚠️ NO PAYMENT INTENT FOUND');
      }
    } catch (stripeError) {
      console.error('❌ ERROR fetching Stripe data:', stripeError);
      // Continue without Stripe data
    }

    return NextResponse.json({
      success: true,
      transaction,
      stripeSession: stripeSession ? {
        id: stripeSession.id,
        amount_total: stripeSession.amount_total,
        currency: stripeSession.currency,
        customer: stripeSession.customer,
        payment_status: stripeSession.payment_status,
        payment_intent: stripeSession.payment_intent,
        created: stripeSession.created,
        expires_at: stripeSession.expires_at,
      } : null,
      paymentIntent: paymentIntent ? {
        id: paymentIntent.id,
        status: paymentIntent.status,
        amount: paymentIntent.amount,
        amount_capturable: paymentIntent.amount_capturable,
        amount_received: paymentIntent.amount_received,
        currency: paymentIntent.currency,
        latest_charge: paymentIntent.latest_charge,
        charges: paymentIntent.charges,
        created: paymentIntent.created,
        capture_method: paymentIntent.capture_method,
      } : null,
    });
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch transaction',
    }, { status: 500 });
  }
}
